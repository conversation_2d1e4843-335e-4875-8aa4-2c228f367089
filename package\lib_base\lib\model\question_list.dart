import 'package:json_annotation/json_annotation.dart';
import 'package:lib_base/model/question_child_list_model.dart';

part 'question_list.g.dart';

@JsonSerializable()
class QuestionList {
  String? id;
  final String? segment;
  final String? subject;
  final String? caption;
  final String? questionType;
  final String? questionChildType;
  final String? userQuestionType;
  final String? twoUserQuestionType;
  final num? answerTime;
  final String? keyword;
  final String? questionKind;
  final List<QuestionOptionList>? questionOptionList;
  final String? titleMp3;
  final String? titleImage;
  final String? title;
  final String? analyze;
  final String? analyzeFile;
  final String? questionNumber;
  final List<QuestionChildListModel>? questionChildList;
  final String? scoreVal;
  final String? knowledgeName;
  final String? resourceIdsStr;
  final String? difficulty;
  final String? titleMp4;
  final String? captionMp3;
  final String? captionTipMp3;
  final num? readTime;
  final String? resourceId;
  final String? wordIdsStr;
  final String? wordId;
  final String? wordName;
  final String? name;
  final String? paraphrase;
  final String? audioFile;
  final String? resourcesName;
  @JsonKey(includeFromJson: false)
  String? _questionTypeName;

  final String? studentResult;
  final String? sysResult;

  String? wdId;

  String? isMust;

  String? mp3File;
  String? imageFile;
  String? twouserQuestionType;

  QuestionList({
    this.id,
    this.segment,
    this.subject,
    this.caption,
    this.questionType,
    this.questionChildType,
    this.userQuestionType,
    this.twoUserQuestionType,
    this.answerTime,
    this.keyword,
    this.questionKind,
    this.questionOptionList,
    this.titleMp3,
    this.titleImage,
    this.title,
    this.analyze,
    this.questionNumber,
    this.questionChildList,
    this.scoreVal,
    this.knowledgeName,
    this.resourceIdsStr,
    this.difficulty,
    this.titleMp4,
    this.captionMp3,
    this.captionTipMp3,
    this.readTime,
    this.resourceId,
    this.wordIdsStr,
    this.wordId,
    this.wordName,
    this.name,
    this.paraphrase,
    this.audioFile,
    this.studentResult,
    this.sysResult,
    this.wdId,
    this.isMust,
    this.mp3File,
    this.imageFile,
    this.analyzeFile,
    this.resourcesName,
    this.twouserQuestionType,
  });

  factory QuestionList.fromJson(Map<String, dynamic> json) =>
      _$QuestionListFromJson(json);

  Map<String, dynamic> toJson() => _$QuestionListToJson(this);

  void setQuestionTypeName(String? questionTypeName) {
    _questionTypeName = questionTypeName;
  }

  String? getQuestionTypeName() {
    return _questionTypeName;
  }
}

@JsonSerializable()
class QuestionOptionList {
  final String? id;
  final String? questionId;
  final String? content;
  final String? isRight;
  final num? sort;
  final num? answerIndex;
  final String? answerIndexStr;
  final String? contentImage;
  final String? contentAudio;
  final bool? rightBool;
  final bool? inputVisible;
  final bool? noInputVisible;
  final List<String>? keyWordTags;

  const QuestionOptionList({
    this.id,
    this.questionId,
    this.content,
    this.isRight,
    this.sort,
    this.answerIndex,
    this.answerIndexStr,
    this.contentImage,
    this.contentAudio,
    this.rightBool,
    this.inputVisible,
    this.noInputVisible,
    this.keyWordTags,
  });

  factory QuestionOptionList.fromJson(Map<String, dynamic> json) =>
      _$QuestionOptionListFromJson(json);

  Map<String, dynamic> toJson() => _$QuestionOptionListToJson(this);
}

@JsonSerializable()
class QuestionChildList {
  final String? id;

  const QuestionChildList({this.id});

  factory QuestionChildList.fromJson(Map<String, dynamic> json) =>
      _$QuestionChildListFromJson(json);

  Map<String, dynamic> toJson() => _$QuestionChildListToJson(this);
}
