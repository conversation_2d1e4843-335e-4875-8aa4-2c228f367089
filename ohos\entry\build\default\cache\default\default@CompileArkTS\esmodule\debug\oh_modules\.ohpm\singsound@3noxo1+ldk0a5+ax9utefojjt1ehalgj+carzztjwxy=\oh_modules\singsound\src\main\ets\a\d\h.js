export var OffLineSourceEnum;
(function (h3) {
    h3["SOURCE_EN"] = "0";
    h3["SOURCE_CH"] = "1";
    h3["SOURCE_BOTN"] = "2";
})(OffLineSourceEnum || (OffLineSourceEnum = {}));
export var CoreProvideTypeEnum;
(function (g3) {
    g3["CLOUD"] = "cloud";
    g3["NATIVE"] = "native";
    g3["AUTO"] = "auto";
})(CoreProvideTypeEnum || (CoreProvideTypeEnum = {}));
export var OralType;
(function (f3) {
    f3[f3["Word"] = 1] = "Word";
    f3[f3["Sentence"] = 2] = "Sentence";
    f3[f3["Paragraph"] = 3] = "Paragraph";
    f3[f3["Choose"] = 4] = "Choose";
    f3[f3["EssayQuestion"] = 5] = "EssayQuestion";
    f3[f3["Picture"] = 6] = "Picture";
    f3[f3["ChineseWord"] = 7] = "ChineseWord";
    f3[f3["ChineseSentence"] = 8] = "ChineseSentence";
    f3[f3["ChinesePcha"] = 9] = "ChinesePcha";
    f3[f3["ChinesePred"] = 10] = "ChinesePred";
    f3[f3["EnglishPcha"] = 11] = "EnglishPcha";
    f3[f3["Alpha"] = 12] = "Alpha";
    f3[f3["Rec"] = 13] = "Rec";
    f3[f3["Pcha"] = 14] = "Pcha";
    f3[f3["Retell"] = 15] = "Retell";
    f3[f3["Pche"] = 16] = "Pche";
    f3[f3["KidWord"] = 17] = "KidWord";
    f3[f3["KidSent"] = 18] = "KidSent";
    f3[f3["Mpd"] = 19] = "Mpd";
    f3[f3["Poet"] = 20] = "Poet";
    f3[f3["CnRec"] = 21] = "CnRec";
    f3[f3["EnRec"] = 22] = "EnRec";
})(OralType || (OralType = {}));
export var EvaluatingPrecision;
(function (e3) {
    e3[e3["Small"] = 0.1] = "Small";
    e3[e3["Medium"] = 0.5] = "Medium";
    e3[e3["High"] = 1] = "High";
})(EvaluatingPrecision || (EvaluatingPrecision = {}));
export var AudioTypeEnum;
(function (d3) {
    d3["PCM"] = "pcm";
    d3["WAV"] = "wav";
    d3["MP3"] = "mp3";
})(AudioTypeEnum || (AudioTypeEnum = {}));
export var AudioTypeForEvaEnum;
(function (c3) {
    c3["MP3"] = "mp3";
    c3["OGG"] = "ogg";
    c3["AMR"] = "amr";
    c3["PCM"] = "pcm";
    c3["WAV"] = "wav";
    c3["opus"] = "opus";
    c3["OGG_OPUS"] = "ogg_opus";
    c3["WECHAT_SPEEX"] = "wechat_speex";
})(AudioTypeForEvaEnum || (AudioTypeForEvaEnum = {}));
