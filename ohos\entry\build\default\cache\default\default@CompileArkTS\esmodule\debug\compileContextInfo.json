{"hspPkgNames": [], "compileEntries": ["&entry/src/main/ets/entryability/EntryAbility&", "&entry/src/main/ets/pages/Index&", "&@ohos/flutter_ohos/src/main/ets/embedding/engine/workers/PlatformChannelWorker&1.0.0-1d86f33900", "&@pdp/book/src/main/ets/page/read/PdpReadBookPage&1.0.0", "&@pdp/book/src/main/ets/page/tape/PdpTapePage&1.0.0"], "updateVersionInfo": {"audioplayers_ohos": {}, "flutter_inappwebview_ohos": {}, "path_provider_ohos": {}, "shared_preferences_ohos": {}, "url_launcher_ohos": {}, "video_player_ohos": {}, "permission_handler_ohos": {}, "package_info_plus": {}, "flutter_sound": {}, "connectivity_plus": {}, "mobile_scanner": {}, "image_picker_ohos": {}, "@pdp/book": {"@ohos/lottie": "2.0.22", "reflect-metadata": "0.2.1", "@pdp/evaluation": "1.1.2"}, "device_info_plus": {}, "fluwx": {}, "flutter_image_compress_ohos": {}, "@pdp/swiper": {"@ohos/lottie": "2.0.22"}, "@pdp/evaluation": {"singsound": "1.0.0", "@ohos/lottie": "2.0.22"}, "@umeng/common": {"@umeng/common": "1.1.3"}, "@umeng/analytics": {"@umeng/common": "1.1.3"}}}