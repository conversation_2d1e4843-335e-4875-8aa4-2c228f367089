import { AudioTypeForEvaEnum, CoreProvideTypeEnum } from "@normalized:N&&&singsound/src/main/ets/a/d/h&1.0.0";
import audio from '@ohos:multimedia.audio';
import { getAAID, isEmpty } from "@normalized:N&&&singsound/src/main/ets/a/p/q&1.0.0";
import { SS_ENGINE_INIT_FAIL, SS_ENGINE_STOP_FAIL, SS_FEED_AUDIO_DATA_FAIL, SS_NO_KEY, SS_RECORDER_START_FAIL, SS_RECORDER_STOP_FAIL, SS_RECORD_AGAIN_LATER, SS_RECORD_INIT_FAIL, SS_REQUEST_ID_NO_MATCH, SS_SERVER_RESULT_ERROR } from "@normalized:N&&&singsound/src/main/ets/a/d/e&1.0.0";
import singNapi, { ssound_log } from '@normalized:Y&&&libsingsound.so&';
import { SingSoundRecorder } from "@normalized:N&&&singsound/src/main/ets/a/m/n&1.0.0";
import fs from '@ohos:file.fs';
import { SSAudioPlayer } from "@normalized:N&&&singsound/src/main/ets/a/m/o&1.0.0";
import { parseCNGResourcePath, parseENGResourcePath } from "@normalized:N&&&singsound/src/main/ets/a/p/r&1.0.0";
import { FileUtils } from "@normalized:N&&&singsound/src/main/ets/a/p/s&1.0.0";
export class SingSoundEngine {
    setServerType(t6) {
        this.setCoreType(t6);
        this.engineStartConfig.coreProvideType = t6;
    }
    setResultListener(s6) {
        this.mResultListener = s6;
    }
    setAudioStreamInfo(r6) {
        this.engineStartConfig.audio.sampleRate = r6;
    }
    setChannel(q6) {
        this.engineStartConfig.audio.channel = q6;
    }
    setServerAPI(p6) {
        if (this.engineInitConfig.cloud) {
            this.engineInitConfig.cloud.server = p6;
        }
    }
    enableRealTimeMode() {
        this.engineStartConfig.request.feedback = 1;
    }
    disableRealTimeMode() {
        this.engineStartConfig.request.feedback = undefined;
    }
    enableVolume() {
        this.engineStartConfig.soundIntensityEnable = 1;
    }
    disableVolume() {
        this.engineStartConfig.soundIntensityEnable = 0;
    }
    setServerTimeout(o6) {
        if (this.engineInitConfig.cloud) {
            this.engineInitConfig.cloud.serverTimeout = o6;
        }
    }
    setConnectTimeout(n6) {
        if (n6 < 5) {
            return false;
        }
        if (this.engineInitConfig.cloud) {
            this.engineInitConfig.cloud.connectTimeout = n6;
        }
        return true;
    }
    setEnableAsync(m6) {
        if (this.engineInitConfig.cloud) {
            this.engineInitConfig.cloud.enableAsync = m6 ? 1 : 0;
        }
    }
    setEnableOffline(l6) {
        this.engineStartConfig.enableContonative = l6 ? 1 : 0;
    }
    setEnableWS(k6) {
        this.isEnableWS = k6;
    }
    setAuthInfo(i6, j6) {
        this.engineStartConfig.app.warrantId = i6;
        this.authTimeout = j6;
    }
    setLogLevel(h6) {
        this.engineInitConfig.logLevel = h6;
    }
    setVadEnable(g6) {
        if (this.engineInitConfig.vad) {
            this.engineInitConfig.vad.vadEnable = g6 ? 1 : 0;
        }
    }
    setAuthServer(f6) {
        if (this.engineInitConfig.native) {
            this.engineInitConfig.native.authenServer = f6;
        }
    }
    setFrontVadTime(e6) {
        if (this.engineInitConfig.vad) {
            this.engineInitConfig.vad.maxBeginSil = e6;
        }
    }
    setBackVadTime(d6) {
        if (this.engineInitConfig.vad) {
            this.engineInitConfig.vad.rightMargin = d6;
        }
    }
    setAudioTypeForEva(c6) {
        this.engineStartConfig.audio.audioType = c6;
    }
    setLocalEvaluationBlankSleepTime(b6) {
        this.sleepTime = b6;
    }
    buildInitJson(z5, a6) {
        if (isEmpty(z5)) {
            this.mResultListener?.onEnd(SS_NO_KEY, '::ssound_log:: appKey is empty');
        }
        this.engineInitConfig.appKey = z5;
        this.engineInitConfig.secretKey = a6;
        return this.engineInitConfig;
    }
    setInitConfig(y5) {
        this.engineInitConfig = y5;
    }
    createEngine(x5) {
        this.setCoreType(x5);
        if (this.getEngine() === undefined || this.getEngine() === null) {
            console.log("::ssound_log:: engine init fail");
            this.mResultListener?.onEnd(SS_ENGINE_INIT_FAIL, 'init fail, please check param');
        }
        else {
            console.log("::ssound_log:: engine init success");
            this.mResultListener?.onReady();
        }
    }
    async initAudio(s5, t5) {
        this.recorder = new SingSoundRecorder((w5) => {
            singNapi.ssound_feed(this.getEngine(), w5, w5.byteLength);
        });
        const u5 = await this.recorder.initAudio(s5, t5);
        if (u5 !== '') {
            this.mResultListener?.onEnd(SS_RECORD_INIT_FAIL, u5);
        }
    }
    buildStartJson(p5, q5, r5) {
        this.engineStartConfig.app.warrantId = p5;
        if (r5) {
            this.engineStartConfig.app.userId = r5;
        }
        if (q5) {
            this.engineStartConfig.request = q5;
        }
        return this.engineStartConfig;
    }
    setStartConfig(o5) {
        this.setCoreType(o5.coreProvideType);
        this.rid = singNapi.ssound_start(this.getEngine(), JSON.stringify(o5), "", SingSoundEngine.callback, "test napi ssound_start");
        if (this.rid === undefined) {
            this.mResultListener?.onEnd(SS_RECORDER_START_FAIL, 'C层start调用失败，请检测start config参数');
        }
        else {
            this.mResultListener?.onGetRequestId(`${this.rid}`);
        }
    }
    async start() {
        this.recorder?.setFileName(`${this.rid}.pcm`);
        this.engineStartConfig.audio.audioType = AudioTypeForEvaEnum.WAV;
        this.engineStartConfig.audio.saveAudio = 0;
        this.engineStartConfig.audio.sampleBytes = 2;
        this.engineStartConfig.app.deviceId = await getAAID();
        this.isPcm = false;
        if (this.recorder?.isRecording()) {
            this.mResultListener?.onEnd(SS_RECORD_AGAIN_LATER, '正在录音中，请先结束其他录音');
            return;
        }
        await this.recorder?.start();
    }
    async startWithPCM(d5) {
        const e5 = fs.accessSync(d5);
        if (e5) {
            this.isPcm = true;
            let f5 = fs.createStreamSync(d5, "r+");
            let g5 = new ArrayBuffer(1024 * 3);
            while (true) {
                const h5 = new Promise(async (k5, l5) => {
                    setTimeout(async () => {
                        let n5 = f5.readSync(g5);
                        console.log("::ssound_log:: read file num ", n5);
                        singNapi.ssound_feed(this.getEngine(), g5, n5);
                        if (n5 <= 0) {
                            k5(0);
                        }
                        else {
                            k5(1);
                        }
                    }, this.sleepTime);
                });
                const i5 = await h5;
                if (i5 === 0) {
                    await f5.close();
                    await this.stop();
                    break;
                }
            }
        }
        else {
            this.mResultListener?.onEnd(SS_FEED_AUDIO_DATA_FAIL, '请传入有效地址的PCM文件路径');
        }
    }
    async stop() {
        let b5 = 0;
        if (this.isPcm) {
            console.info('::ssound_log::Capturer stop success.');
            b5 = singNapi.ssound_stop(this.getEngine());
            console.info('::ssound_log:: ssound_stop status.', b5);
        }
        else {
            const c5 = await this.recorder?.stop();
            b5 = singNapi.ssound_stop(this.getEngine());
            console.info('::ssound_log:: ssound_stop status.', b5);
            if (c5 !== '') {
                this.mResultListener?.onEnd(SS_RECORDER_STOP_FAIL, `record stop error, ${c5}`);
            }
            else {
                console.info('::ssound_log::Capturer stop success.');
            }
        }
        if (b5 !== 0) {
            this.mResultListener?.onEnd(SS_ENGINE_STOP_FAIL, "engine stop error");
        }
    }
    cancel() {
        this.recorder?.stop();
        singNapi.ssound_cancel(this.getEngine());
    }
    delete() {
        if (this.recorder) {
            this.recorder.stop();
        }
        if (this.getEngine()) {
        }
    }
    deleteSafe() {
        if (this.recorder) {
            this.recorder.stop();
        }
        this.delete();
    }
    getWavPath() {
        return this.recorder?.filePath ?? '';
    }
    playbackWithPath(x4) {
        const y4 = SSAudioPlayer.getInstance();
        this.mPlayer = y4;
        y4.audioStreamInfo = {
            samplingRate: this.engineStartConfig.audio.sampleRate,
            channels: this.engineStartConfig.audio.channel,
            sampleFormat: audio.AudioSampleFormat.SAMPLE_FORMAT_S16LE,
            encodingType: audio.AudioEncodingType.ENCODING_TYPE_RAW
        };
        const z4 = y4.setFilePath(x4);
        if (z4 instanceof Error) {
            return;
        }
        y4.init().then(() => {
            y4.start();
        });
    }
    playback() {
        this.playbackWithPath(this.getWavPath());
    }
    stopPlayBack() {
        this.mPlayer?.stop().then(() => {
            this.mPlayer?.release();
        });
    }
    async setCoreType(v4) {
        this.coreProvideType = v4;
        if (v4 === CoreProvideTypeEnum.NATIVE) {
            if (this.nativeEngine === undefined) {
                this.nativeEngine = singNapi.ssound_new(JSON.stringify(this.engineInitConfig));
            }
        }
        else {
            if (this.cloudEngine === undefined) {
                this.cloudEngine = singNapi.ssound_new(JSON.stringify(this.engineInitConfig));
            }
        }
        if (this.recorder === undefined) {
            await this.initAudio(this.engineStartConfig.audio.channel, this.engineStartConfig.audio.sampleRate);
        }
    }
    getEngine() {
        if (this.coreProvideType === CoreProvideTypeEnum.NATIVE) {
            return this.nativeEngine;
        }
        else {
            return this.cloudEngine;
        }
    }
    constructor() {
        this.isEnableWS = false;
        this.authTimeout = 10;
        this.sleepTime = 10;
        this.coreProvideType = CoreProvideTypeEnum.CLOUD;
        const n4 = getContext().cacheDir;
        const o4 = {
            "enable": 1,
            "output": `${n4}/crash.txt`
        };
        const p4 = {
            "enable": 1,
            "server": "wss://api.cloud.ssapi.cn",
            "connectTimeout": 20,
            "serverTimeout": 60,
            "enableAsync": 0
        };
        const q4 = {
            "appKey": "",
            "secretKey": "",
            "logEnable": 1,
            "logLevel": 4,
            "prof": o4,
            "cloud": p4
        };
        this.engineInitConfig = q4;
        this.isPcm = false;
        const r4 = {
            "userId": "guest",
            "deviceId": '',
            "warrantId": ''
        };
        const s4 = {
            "saveAudio": 0,
            "audioType": AudioTypeForEvaEnum.WAV,
            "sampleRate": 16000,
            "sampleBytes": 2,
            "channel": 1
        };
        const t4 = {
            "coreType": "en.sent.score",
            "refText": "I don't play with you, because you(c:1) are a bad(c:1) boy(t:0).",
            "rank": 100,
            "symbol": 1,
            "feedback": 0,
            "attachAudioUrl": 1
        };
        const u4 = {
            "coreProvideType": CoreProvideTypeEnum.CLOUD,
            "soundIntensityEnable": 0,
            "enableRetry": 1,
            "enableContonative": 0,
            "app": r4,
            "audio": s4,
            "request": t4
        };
        this.engineStartConfig = u4;
    }
    static getInstance() {
        if (SingSoundEngine.instance === null) {
            SingSoundEngine.instance = new SingSoundEngine();
        }
        return SingSoundEngine.instance;
    }
    getVersionName() {
        return 'v1.5.1';
    }
    async nativeResInstall(g4) {
        return new Promise((i4, j4) => {
            FileUtils.unzipRawFileHandler(g4, 'resources.zip').then((m4) => {
                AppStorage.setOrCreate('NATIVE_RES_FILES', m4);
                PersistentStorage.persistProp('NATIVE_RES_FILES', m4);
                i4(m4);
            }, () => {
                j4("::ssound_log:: 离线资源文件安装失败");
            });
        });
    }
    buildNativeCfg() {
        const t3 = AppStorage.get('NATIVE_RES_FILES');
        if (!t3)
            return undefined;
        console.log('::ssound_log:: 查看文件列表:' + t3);
        const u3 = `${t3}/eval/bin`;
        let v3 = FileUtils.getSandboxFileList(u3);
        ;
        if (v3 === undefined) {
            console.log("::ssound_log:: native config 读取离线资源文件错误");
        }
        const w3 = [];
        for (let f4 = 0; f4 < v3.length; f4++) {
            console.info(`::ssound_log:: The name of file: ${v3[f4]}`);
            w3.push(`${u3}/${v3[f4]}`);
        }
        const x3 = [];
        for (const c4 of w3) {
            const d4 = c4.split('/');
            const e4 = d4.length - 1;
            x3.push({
                fileName: d4[e4],
                filePath: c4,
            });
        }
        let y3;
        if (x3) {
            parseCNGResourcePath(x3);
            parseENGResourcePath(x3);
            const z3 = new Object;
            for (const a4 of x3) {
                const b4 = {
                    res: a4.filePath
                };
                z3[a4.key ?? ""] = b4;
            }
            y3 = z3;
            return y3;
        }
        return {};
    }
}
SingSoundEngine.callback = (i3, j3, k3, l3, m3) => {
    try {
        let o3 = l3;
        if (isEmpty(o3)) {
            return 0;
        }
        const p3 = JSON.parse(o3);
        if (p3 === undefined || p3 === null) {
            SingSoundEngine.getInstance().stop();
            const s3 = "::ssound_log:: ssound_start callback resultJson is empty";
            ssound_log(SingSoundEngine.getInstance().getEngine(), s3);
            SingSoundEngine.getInstance().mResultListener?.onEnd(SS_SERVER_RESULT_ERROR, s3);
            return 0;
        }
        if (j3 === undefined || j3 === null) {
            const r3 = "::ssound_log:: ssound_start callback message is empty";
            console.log(r3);
            SingSoundEngine.getInstance().mResultListener?.onEnd(SS_REQUEST_ID_NO_MATCH, r3);
            SingSoundEngine.getInstance().stop();
            return 0;
        }
        if (p3['errId']) {
            SingSoundEngine.getInstance().mResultListener?.onEnd(p3['errId'], p3['error']);
            console.log('Stop: when return error');
            SingSoundEngine.getInstance().stop();
            return 0;
        }
        else if ((p3['vad_status']) || (p3['sound_intensity'])) {
            const q3 = p3['vad_status'];
            console.log("::ssound_log:: ssound_start callback vad_", JSON.stringify(p3));
            if (q3 === 2) {
                SingSoundEngine.getInstance().stop();
                SingSoundEngine.getInstance().mResultListener?.onBackVadTimeOut();
            }
            if (q3 === 3) {
                SingSoundEngine.getInstance().mResultListener?.onFrontVadTimeOut();
            }
        }
        else {
            SingSoundEngine.getInstance().mResultListener?.onResult(p3);
        }
    }
    catch (n3) {
        console.error("::ssound_log:: ssound_start callback error-> ", n3);
    }
    return 0;
};
SingSoundEngine.instance = null;
