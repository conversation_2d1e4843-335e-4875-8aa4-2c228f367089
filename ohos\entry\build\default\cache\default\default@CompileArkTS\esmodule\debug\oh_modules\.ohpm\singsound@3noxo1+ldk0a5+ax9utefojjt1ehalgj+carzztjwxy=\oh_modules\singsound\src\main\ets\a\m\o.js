import audio from "@ohos:multimedia.audio";
import fileIo from "@ohos:file.fs";
const TAG = '::ssound_log::';
class Options {
}
export class SSAudioPlayer {
    constructor() {
        this.bufferSize = 0;
        this.renderModel = undefined;
        this.audioStreamInfo = {
            samplingRate: audio.AudioSamplingRate.SAMPLE_RATE_16000,
            channels: audio.AudioChannel.CHANNEL_1,
            sampleFormat: audio.AudioSampleFormat.SAMPLE_FORMAT_S16LE,
            encodingType: audio.AudioEncodingType.ENCODING_TYPE_RAW
        };
        this.audioRendererInfo = {
            usage: audio.StreamUsage.STREAM_USAGE_MUSIC,
            rendererFlags: 0
        };
        this.audioRendererOptions = {
            streamInfo: this.audioStreamInfo,
            rendererInfo: this.audioRendererInfo
        };
        this.writeDataCallback = (a3) => {
            let b3 = {
                offset: this.bufferSize,
                length: a3.byteLength
            };
            if (this.file?.fd === undefined || a3 === undefined) {
                console.error(`::ssound_log:: audio player writeData failed, file is ${this.file}, buffer is ${a3}`);
                return;
            }
            fileIo.readSync(this.file?.fd, a3, b3);
            this.bufferSize += a3.byteLength;
        };
    }
    static getInstance() {
        if (SSAudioPlayer._instance === null) {
            SSAudioPlayer._instance = new SSAudioPlayer();
        }
        return SSAudioPlayer._instance;
    }
    setFilePath(x2) {
        try {
            this.file = fileIo.openSync(x2, fileIo.OpenMode.READ_ONLY);
            return this.file;
        }
        catch (y2) {
            console.error(TAG + "setFilePath fail " + JSON.stringify(y2));
            return y2;
        }
    }
    init() {
        return new Promise((s2, t2) => {
            audio.createAudioRenderer(this.audioRendererOptions, (v2, w2) => {
                if (!v2) {
                    console.info(`${TAG}: creating AudioRenderer success`);
                    this.renderModel = w2;
                    if (this.renderModel !== undefined) {
                        this.renderModel.on('writeData', this.writeDataCallback);
                    }
                    s2(true);
                }
                else {
                    console.info(`${TAG}: creating AudioRenderer failed, error: ${v2.message}`);
                    t2(v2);
                }
            });
        });
    }
    start() {
        if (this.renderModel !== undefined) {
            this.renderModel.state;
            let o2 = [audio.AudioState.STATE_PREPARED, audio.AudioState.STATE_PAUSED, audio.AudioState.STATE_STOPPED];
            if (o2.indexOf(this.renderModel.state.valueOf()) === -1) {
                console.error(TAG + 'start failed');
                return;
            }
            this.bufferSize = 0;
            this.renderModel.start((q2) => {
                if (q2) {
                    console.error(TAG + 'Renderer start failed.');
                }
                else {
                    console.info(TAG + 'Renderer start success.');
                }
            });
        }
    }
    pause() {
        if (this.renderModel !== undefined) {
            if (this.renderModel.state.valueOf() !== audio.AudioState.STATE_RUNNING) {
                console.info(TAG + 'Renderer is not running');
                return;
            }
            this.renderModel.pause((n2) => {
                if (n2) {
                    console.error(TAG + 'Renderer pause failed.');
                }
                else {
                    console.info(TAG + 'Renderer pause success.');
                }
            });
        }
    }
    async stop() {
        if (this.renderModel !== undefined) {
            if (this.renderModel.state.valueOf() !== audio.AudioState.STATE_RUNNING && this.renderModel.state.valueOf() !== audio.AudioState.STATE_PAUSED) {
                console.info(TAG + 'Renderer is not running or paused.');
                return;
            }
            this.renderModel.stop((l2) => {
                if (l2) {
                    console.error(TAG + 'Renderer stop failed.');
                }
                else {
                    this.bufferSize = 0;
                    fileIo.close(this.file);
                    console.info(TAG + 'Renderer stop success.');
                }
            });
        }
    }
    async release() {
        if (this.renderModel !== undefined) {
            if (this.renderModel.state.valueOf() === audio.AudioState.STATE_RELEASED) {
                console.info('TAG + Renderer already released');
                return;
            }
            this.renderModel.release((j2) => {
                if (j2) {
                    console.error(TAG + 'Renderer release failed.');
                }
                else {
                    console.info(TAG + 'Renderer release success.');
                }
            });
        }
    }
}
SSAudioPlayer._instance = null;
