import singNapi from '@normalized:Y&&&libsingsound.so&';
export function ssound_new(n7) {
    return singNapi.ssound_new(n7);
}
export function ssound_start(i7, j7, k7, l7, m7) {
    return singNapi.ssound_start(i7, j7, k7, l7, m7);
}
export function ssound_delete(h7) {
    return singNapi.ssound_delete(h7);
}
export function ssound_feed(e7, f7, g7) {
    return singNapi.ssound_feed(e7, f7, g7);
}
export function ssound_stop(d7) {
    return singNapi.ssound_stop(d7);
}
export function ssound_log(b7, c7) {
    return singNapi.ssound_log(b7, c7);
}
export function ssound_get_device_id(a7) {
    return singNapi.ssound_get_device_id(a7);
}
export function ssound_cancel(z6) {
    return singNapi.ssound_cancel(z6);
}
export function ssound_opt(v6, w6, x6, y6) {
    return singNapi.ssound_opt(v6, w6, x6, y6);
}
export function ssound_get_result(u6) {
    return singNapi.ssound_get_result(u6);
}
