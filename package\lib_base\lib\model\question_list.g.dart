// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'question_list.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QuestionList _$QuestionListFromJson(Map<String, dynamic> json) => QuestionList(
      id: json['id'] as String?,
      segment: json['segment'] as String?,
      subject: json['subject'] as String?,
      caption: json['caption'] as String?,
      questionType: json['questionType'] as String?,
      questionChildType: json['questionChildType'] as String?,
      userQuestionType: json['userQuestionType'] as String?,
      twoUserQuestionType: json['twoUserQuestionType'] as String?,
      answerTime: json['answerTime'] as num?,
      keyword: json['keyword'] as String?,
      questionKind: json['questionKind'] as String?,
      questionOptionList: (json['questionOptionList'] as List<dynamic>?)
          ?.map((e) => QuestionOptionList.fromJson(e as Map<String, dynamic>))
          .toList(),
      titleMp3: json['titleMp3'] as String?,
      titleImage: json['titleImage'] as String?,
      title: json['title'] as String?,
      analyze: json['analyze'] as String?,
      questionNumber: json['questionNumber'] as String?,
      questionChildList: (json['questionChildList'] as List<dynamic>?)
          ?.map(
              (e) => QuestionChildListModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      scoreVal: json['scoreVal'] as String?,
      knowledgeName: json['knowledgeName'] as String?,
      resourceIdsStr: json['resourceIdsStr'] as String?,
      difficulty: json['difficulty'] as String?,
      titleMp4: json['titleMp4'] as String?,
      captionMp3: json['captionMp3'] as String?,
      captionTipMp3: json['captionTipMp3'] as String?,
      readTime: json['readTime'] as num?,
      resourceId: json['resourceId'] as String?,
      wordIdsStr: json['wordIdsStr'] as String?,
      wordId: json['wordId'] as String?,
      wordName: json['wordName'] as String?,
      name: json['name'] as String?,
      paraphrase: json['paraphrase'] as String?,
      audioFile: json['audioFile'] as String?,
      studentResult: json['studentResult'] as String?,
      sysResult: json['sysResult'] as String?,
      wdId: json['wdId'] as String?,
      isMust: json['isMust'] as String?,
      mp3File: json['mp3File'] as String?,
      imageFile: json['imageFile'] as String?,
      analyzeFile: json['analyzeFile'] as String?,
      resourcesName: json['resourcesName'] as String?,
      twouserQuestionType: json['twouserQuestionType'] as String?,
    );

Map<String, dynamic> _$QuestionListToJson(QuestionList instance) =>
    <String, dynamic>{
      'id': instance.id,
      'segment': instance.segment,
      'subject': instance.subject,
      'caption': instance.caption,
      'questionType': instance.questionType,
      'questionChildType': instance.questionChildType,
      'userQuestionType': instance.userQuestionType,
      'twoUserQuestionType': instance.twoUserQuestionType,
      'answerTime': instance.answerTime,
      'keyword': instance.keyword,
      'questionKind': instance.questionKind,
      'questionOptionList': instance.questionOptionList,
      'titleMp3': instance.titleMp3,
      'titleImage': instance.titleImage,
      'title': instance.title,
      'analyze': instance.analyze,
      'analyzeFile': instance.analyzeFile,
      'questionNumber': instance.questionNumber,
      'questionChildList': instance.questionChildList,
      'scoreVal': instance.scoreVal,
      'knowledgeName': instance.knowledgeName,
      'resourceIdsStr': instance.resourceIdsStr,
      'difficulty': instance.difficulty,
      'titleMp4': instance.titleMp4,
      'captionMp3': instance.captionMp3,
      'captionTipMp3': instance.captionTipMp3,
      'readTime': instance.readTime,
      'resourceId': instance.resourceId,
      'wordIdsStr': instance.wordIdsStr,
      'wordId': instance.wordId,
      'wordName': instance.wordName,
      'name': instance.name,
      'paraphrase': instance.paraphrase,
      'audioFile': instance.audioFile,
      'resourcesName': instance.resourcesName,
      'studentResult': instance.studentResult,
      'sysResult': instance.sysResult,
      'wdId': instance.wdId,
      'isMust': instance.isMust,
      'mp3File': instance.mp3File,
      'imageFile': instance.imageFile,
      'twouserQuestionType': instance.twouserQuestionType,
    };

QuestionOptionList _$QuestionOptionListFromJson(Map<String, dynamic> json) =>
    QuestionOptionList(
      id: json['id'] as String?,
      questionId: json['questionId'] as String?,
      content: json['content'] as String?,
      isRight: json['isRight'] as String?,
      sort: json['sort'] as num?,
      answerIndex: json['answerIndex'] as num?,
      answerIndexStr: json['answerIndexStr'] as String?,
      contentImage: json['contentImage'] as String?,
      contentAudio: json['contentAudio'] as String?,
      rightBool: json['rightBool'] as bool?,
      inputVisible: json['inputVisible'] as bool?,
      noInputVisible: json['noInputVisible'] as bool?,
      keyWordTags: (json['keyWordTags'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$QuestionOptionListToJson(QuestionOptionList instance) =>
    <String, dynamic>{
      'id': instance.id,
      'questionId': instance.questionId,
      'content': instance.content,
      'isRight': instance.isRight,
      'sort': instance.sort,
      'answerIndex': instance.answerIndex,
      'answerIndexStr': instance.answerIndexStr,
      'contentImage': instance.contentImage,
      'contentAudio': instance.contentAudio,
      'rightBool': instance.rightBool,
      'inputVisible': instance.inputVisible,
      'noInputVisible': instance.noInputVisible,
      'keyWordTags': instance.keyWordTags,
    };

QuestionChildList _$QuestionChildListFromJson(Map<String, dynamic> json) =>
    QuestionChildList(
      id: json['id'] as String?,
    );

Map<String, dynamic> _$QuestionChildListToJson(QuestionChildList instance) =>
    <String, dynamic>{
      'id': instance.id,
    };
