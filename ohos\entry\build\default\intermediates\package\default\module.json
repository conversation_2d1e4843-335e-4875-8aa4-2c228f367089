{"app": {"bundleName": "com.yyb.hm", "vendor": "example", "versionCode": 824, "versionName": "8.2.4", "icon": "$media:app_icon", "label": "$string:app_name", "apiReleaseType": "Release", "compileSdkVersion": "*********", "targetAPIVersion": 50005017, "minAPIVersion": 50000012, "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app", "buildMode": "debug", "debug": true, "iconId": 16777217, "labelId": 16777216}, "module": {"name": "entry", "type": "entry", "description": "$string:module_desc", "mainElement": "EntryAbility", "deviceTypes": ["phone", "tablet"], "deliveryWithInstall": true, "installationFree": false, "pages": "$profile:main_pages", "abilities": [{"name": "EntryAbility", "srcEntry": "./ets/entryability/EntryAbility.ets", "description": "$string:EntryAbility_desc", "icon": "$media:icon", "label": "$string:EntryAbility_label", "startWindowIcon": "$media:icon", "startWindowBackground": "$color:start_window_background", "exported": true, "skills": [{"entities": ["entity.system.home", "entity.system.browsable"], "actions": ["action.system.home", "ohos.want.action.viewData"], "uris": [{"scheme": "alipay"}, {"scheme": "alipay:"}]}], "backgroundModes": ["audioPlayback", "audioRecording"], "descriptionId": 16777218, "iconId": 16777226, "labelId": 16777219, "startWindowIconId": 16777226, "startWindowBackgroundId": 16777225}], "querySchemes": ["weixin"], "requestPermissions": [{"name": "ohos.permission.INTERNET"}, {"name": "ohos.permission.GET_NETWORK_INFO"}, {"name": "ohos.permission.MICROPHONE", "reason": "$string:microPhone_reason", "usedScene": {"abilities": ["FormAbility"], "when": "inuse"}, "reasonId": 16777220}, {"name": "ohos.permission.WRITE_MEDIA", "reason": "$string:reason_write_media", "usedScene": {"abilities": ["FormAbility"], "when": "inuse"}, "reasonId": 16777224}, {"name": "ohos.permission.CAMERA", "reason": "$string:reason_camera", "usedScene": {"abilities": ["FormAbility"], "when": "inuse"}, "reasonId": 16777223}, {"name": "ohos.permission.PRIVACY_WINDOW"}, {"name": "ohos.permission.VIBRATE"}, {"name": "ohos.permission.STORE_PERSISTENT_DATA"}, {"name": "ohos.permission.KEEP_BACKGROUND_RUNNING"}, {"name": "ohos.permission.APP_TRACKING_CONSENT", "reason": "$string:reason_oaid", "usedScene": {"when": "inuse"}, "reasonId": 16777233}], "packageName": "entry", "virtualMachine": "ark12.0.2.0", "compileMode": "esmodule", "dependencies": [], "descriptionId": 16777221, "routerMap": "$profile:default-router-map"}}