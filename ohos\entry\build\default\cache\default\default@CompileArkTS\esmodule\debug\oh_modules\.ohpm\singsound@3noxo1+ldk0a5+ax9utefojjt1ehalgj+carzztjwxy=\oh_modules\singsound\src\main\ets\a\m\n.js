import audio from '@ohos:multimedia.audio';
import fs from '@ohos:file.fs';
class Options {
}
export class SingSoundRecorder {
    constructor(d2) {
        this.bufferSize = 0;
        this.audioCapturer = undefined;
        this.readDataCallback = (f2) => {
            if (this.file === undefined) {
                console.log("::ssound_log:: 请先设置 setFileName ");
                return;
            }
            let g2 = {
                offset: this.bufferSize,
                length: f2.byteLength
            };
            try {
                if (this.file?.fd === undefined || f2 === undefined) {
                    console.error(`::ssound_log:: audio player writeData failed, file is ${this.file}, buffer is ${f2}`);
                    return;
                }
                fs.writeSync(this.file.fd, f2, g2);
            }
            catch (h2) {
                console.error(`::ssound_log:: audio player writeData failed, error is ${h2}`);
            }
            this.bufferSize += f2.byteLength;
            if (f2) {
                if (this.onAudioReadDataCallBack) {
                    this.onAudioReadDataCallBack(f2);
                }
            }
            else {
                console.log("::ssound_log:: readDataCallback buffer is null!");
            }
        };
        this.onAudioReadDataCallBack = d2;
    }
    setFileName(a2) {
        this.bufferSize = 0;
        this.fileName = a2;
        const b2 = getContext().cacheDir + '/singSound/';
        const c2 = fs.accessSync(b2);
        if (!c2) {
            fs.mkdirSync(b2);
        }
        this.filePath = b2 + this.fileName;
        this.file = fs.openSync(this.filePath, fs.OpenMode.READ_WRITE | fs.OpenMode.CREATE);
        console.info(`::ssound_log:: audio file open success`);
    }
    initAudio(o1, p1) {
        const q1 = {
            samplingRate: p1 ?? audio.AudioSamplingRate.SAMPLE_RATE_16000,
            channels: o1 ?? audio.AudioChannel.CHANNEL_1,
            sampleFormat: audio.AudioSampleFormat.SAMPLE_FORMAT_S16LE,
            encodingType: audio.AudioEncodingType.ENCODING_TYPE_RAW
        };
        const r1 = {
            source: audio.SourceType.SOURCE_TYPE_MIC,
            capturerFlags: 0
        };
        const s1 = {
            streamInfo: q1,
            capturerInfo: r1
        };
        return new Promise((u1, v1) => {
            audio.createAudioCapturer(s1, (x1, y1) => {
                if (x1) {
                    const z1 = `::ssound_log::Invoke createAudioCapturer failed, code is ${x1.code}, message is ${x1.message}`;
                    console.error(z1);
                    v1(z1);
                    return;
                }
                this.audioCapturer = y1;
                if (this.audioCapturer !== undefined) {
                    console.info(`::ssound_log:: create AudioCapturer success`);
                    this.audioCapturer.on('readData', this.readDataCallback);
                    u1('');
                }
                else {
                    v1(`::ssound_log:: AudioCapturer is undefined`);
                }
            });
        });
    }
    isRecording() {
        return this.audioCapturer.state.valueOf() === audio.AudioState.STATE_RUNNING;
    }
    start() {
        return new Promise((i1, j1) => {
            if (this.audioCapturer !== undefined) {
                let k1 = [audio.AudioState.STATE_PREPARED, audio.AudioState.STATE_PAUSED, audio.AudioState.STATE_STOPPED];
                if (k1.indexOf(this.audioCapturer.state.valueOf()) ===
                    -1) {
                    console.error(`::ssound_log:: start failed`);
                    j1(`::ssound_log:: start failed, status is wrong`);
                    return;
                }
                this.audioCapturer.start((m1) => {
                    if (m1) {
                        const n1 = `::ssound_log::Capturer start failed, code is ${m1.code}, message is ${m1.message}`;
                        console.error(n1);
                        j1(n1);
                    }
                    else {
                        console.info('::ssound_log::Capturer start success.');
                        i1('');
                    }
                });
            }
            else {
                j1(`::ssound_log:: start failed, audioCapturer is undefined`);
            }
        });
    }
    stop() {
        return new Promise((c1, d1) => {
            if (this.audioCapturer !== undefined) {
                if (this.audioCapturer.state.valueOf() !== audio.AudioState.STATE_RUNNING &&
                    this.audioCapturer.state.valueOf() !== audio.AudioState.STATE_PAUSED) {
                    console.info('::ssound_log::Capturer is not running or paused');
                    d1(`::ssound_log:: stop failed, status is wrong, Capturer is not running or paused`);
                    return;
                }
                this.audioCapturer.stop(async (f1) => {
                    if (f1) {
                        const g1 = `::ssound_log::Capturer stop failed, code is ${f1.code}, message is ${f1.message}`;
                        console.error(g1);
                        d1(g1);
                    }
                    else {
                        await fs.close(this.file);
                        console.info('::ssound_log::Capturer stop success.');
                        c1('');
                    }
                });
            }
            else {
                d1(`::ssound_log:: stop failed, audioCapturer is undefined`);
            }
        });
    }
    release() {
        return new Promise(async (w, x) => {
            if (this.audioCapturer !== undefined) {
                if (this.audioCapturer.state.valueOf() === audio.AudioState.STATE_RELEASED ||
                    this.audioCapturer.state.valueOf() === audio.AudioState.STATE_NEW) {
                    console.info('::ssound_log::Capturer already released');
                    w('');
                }
                else {
                    this.audioCapturer.release((z) => {
                        if (z) {
                            const a1 = `::ssound_log::Capturer release failed, code is ${z.code}, message is ${z.message}`;
                            console.error(a1);
                            x(a1);
                        }
                        else {
                            console.info('::ssound_log::Capturer release success.');
                            w('');
                        }
                    });
                }
            }
            else {
                w('');
            }
        });
    }
}
