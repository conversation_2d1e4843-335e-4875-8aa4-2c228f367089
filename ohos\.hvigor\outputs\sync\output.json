{"ohos-module-entry": {"SELECT_TARGET": "default", "MODULE_BUILD_DIR": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build", "DEPENDENCY_INFO": {"audioplayers_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\audioplayers_ohos@vjqvyb+auqibm3hvld60p18eeaxts+gxrdlu5mscy3w=\\oh_modules\\audioplayers_ohos", "flutter_inappwebview_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\flutter_inappwebview_ohos@nnn5tx19jbfoynft9y2uxgzzn38jwtblqyh8rvs5esq=\\oh_modules\\flutter_inappwebview_ohos", "path_provider_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\path_provider_ohos@5reyzj21cehnv+8hh2bs4+eyozj5l9wycgzc8z91doa=\\oh_modules\\path_provider_ohos", "shared_preferences_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\shared_preferences_ohos@krrhlc+x42ly+ybvlywrumh3tr+o6lledirxhkccba0=\\oh_modules\\shared_preferences_ohos", "url_launcher_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\url_launcher_ohos@ubvjnk0ho0rugy8xjojt4bz10ejwb3p1nln+f4ffers=\\oh_modules\\url_launcher_ohos", "video_player_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\video_player_ohos@yigina8+ahk5cbplqnjbph3kak2ynytksr9jhqcwr0c=\\oh_modules\\video_player_ohos", "permission_handler_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\permission_handler_ohos@1ij8v4d7qd06vl+w7qxqe9rmxyt7edukhxqoheqe7bq=\\oh_modules\\permission_handler_ohos", "package_info_plus": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\package_info_plus@+vsw4huh8cdap7mfrkk1mpx2lvsokdkts7pr572+1so=\\oh_modules\\package_info_plus", "flutter_sound": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\flutter_sound@csvgtogygjhnxx9xytpedgilsvpygxrwnp8edc9h1bo=\\oh_modules\\flutter_sound", "connectivity_plus": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\connectivity_plus@fstse6plfoqp7tihkalyc4q6purwzm5wjoorcezrio0=\\oh_modules\\connectivity_plus", "mobile_scanner": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\mobile_scanner@pqxwp9bvahb8hcvl+ipuceqdp9yy37god9vfssgwyug=\\oh_modules\\mobile_scanner", "image_picker_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\image_picker_ohos@v5khmgf6eigjalhb2xhap5n+kv2bjzkwru3ffot2wc8=\\oh_modules\\image_picker_ohos", "@pdp/book": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@pdp+book@kwetdlm8p3fehla1xud5h+ejryzid8z0alb9exoy9vg=\\oh_modules\\@pdp\\book", "singsound": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\singsound@3noxo1+ldk0a5+ax9utefojjt1ehalgj+carzztjwxy=\\oh_modules\\singsound", "device_info_plus": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\device_info_plus@0j4+bgrp3onis2v6bxdt7ygy7zhru5siigujy9u7gbg=\\oh_modules\\device_info_plus", "fluwx": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\fluwx@hk7+gp+jk2ahadjyn3pjpu3ppt2+isqe62qtsblp2pe=\\oh_modules\\fluwx", "flutter_image_compress_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\flutter_image_compress_ohos@ml7hvqg96wpjf8gdropkzbav7nbpwijc+njxlcqu6kq=\\oh_modules\\flutter_image_compress_ohos", "@ohos/flutter_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@ohos+flutter_ohos@9kqpxxvrkevrqywwhtpiewkd1pgqyw5lf7dxvh2sbiq=\\oh_modules\\@ohos\\flutter_ohos", "@ohos/lottie": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@ohos+lottie@2.0.22\\oh_modules\\@ohos\\lottie", "@ohos/crypto-js": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@ohos+crypto-js@2.0.4\\oh_modules\\@ohos\\crypto-js", "@pdp/swiper": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@pdp+swiper@1.0.0\\oh_modules\\@pdp\\swiper", "@pdp/evaluation": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@pdp+evaluation@1.1.2\\oh_modules\\@pdp\\evaluation", "bigdata": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\bigdata@mptnw0wkm5spww48ifee6omoheeuqmqsa3pwhpz9qpk=\\oh_modules\\bigdata", "@tencent/wechat_open_sdk": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@tencent+wechat_open_sdk@1.0.11\\oh_modules\\@tencent\\wechat_open_sdk", "@ohos/mp4parser": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@ohos+mp4parser@2.0.3-rc.1\\oh_modules\\@ohos\\mp4parser", "@umeng/common": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@umeng+common@1.1.3\\oh_modules\\@umeng\\common", "@umeng/analytics": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@umeng+analytics@1.2.4\\oh_modules\\@umeng\\analytics"}, "TARGETS": {"default": {"SOURCE_ROOT": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\src\\main", "RESOURCES_PATH": ["D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\src\\main\\resources"], "BUILD_PATH": {"OUTPUT_PATH": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\outputs\\default", "INTERMEDIA_PATH": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates", "JS_ASSETS_PATH": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\loader_out\\default", "JS_LITE_ASSETS_PATH": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\loader_out_lite\\default", "RES_PATH": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\res\\default", "RES_PROFILE_PATH": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", "ETS_SUPER_VISUAL_PATH": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\cache\\default\\default@CompileArkTS\\esmodule", "JS_SUPER_VISUAL_PATH": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\cache\\default\\default@CompileJS\\jsbundle", "WORKER_LOADER": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\loader\\default\\loader.json", "MANIFEST_JSON": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\manifest\\default", "OUTPUT_METADATA_JSON": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json", "SOURCE_MAP_DIR": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\source_map\\default"}, "BUILD_OPTION": {"debuggable": true}}, "ohosTest": {"SOURCE_ROOT": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\src\\ohosTest", "RESOURCES_PATH": ["D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\src\\ohosTest\\resources"], "BUILD_PATH": {"OUTPUT_PATH": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\outputs\\ohosTest", "INTERMEDIA_PATH": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates", "JS_ASSETS_PATH": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\loader_out\\ohosTest", "JS_LITE_ASSETS_PATH": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\loader_out_lite\\ohosTest", "RES_PATH": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\res\\ohosTest", "RES_PROFILE_PATH": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\res\\ohosTest\\resources\\base\\profile", "ETS_SUPER_VISUAL_PATH": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\cache\\ohosTest\\ohosTest@OhosTestCompileArkTS\\esmodule", "JS_SUPER_VISUAL_PATH": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\cache\\ohosTest\\ohosTest@OhosTestCompileJS\\jsbundle", "WORKER_LOADER": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\loader\\ohosTest\\loader.json", "MANIFEST_JSON": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\manifest\\ohosTest", "OUTPUT_METADATA_JSON": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\hap_metadata\\ohosTest\\output_metadata.json", "SOURCE_MAP_DIR": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\source_map\\ohosTest"}, "BUILD_OPTION": {"debuggable": true}}}, "BUILD_OPTION": {"default-default": {"debuggable": true, "copyFrom": "default", "strictMode": {"useNormalizedOHMUrl": true, "noExternalImportByPath": true}}}, "BUILD_PROFILE_OPT": {"apiType": "stageMode", "buildOption": {}, "targets": [{"name": "default", "runtimeOS": "HarmonyOS"}, {"name": "ohosTest", "runtimeOS": "HarmonyOS"}]}, "BUILD_CACHE_DIR": ""}, "ohos-project": {"SELECT_PRODUCT_NAME": "default", "MODULE_BUILD_DIR": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\build", "BUNDLE_NAME": "com.yyb.hm", "BUILD_PATH": {"OUTPUT_PATH": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\build\\outputs\\default"}, "MODULES": [{"name": "entry", "srcPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry", "targets": [{"name": "default", "applyToProducts": ["default"]}], "belongProjectPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos"}], "PROFILE_OPT": {"app": {"signingConfigs": [{"name": "default", "type": "HarmonyOS", "material": {"certpath": "C:\\Users\\<USER>\\.ohos\\config\\default_ohos_efqy5cCLQ5GOYdEYaiRHctV-XouwM1QY4MBAuJlpJMs=.cer", "keyAlias": "debugKey", "keyPassword": "0000001ADA550264631997BAD3380CFE50A1A62DBCF794C709FBF200609B6953C74D2103CF4977A736D6", "profile": "C:\\Users\\<USER>\\.ohos\\config\\default_ohos_efqy5cCLQ5GOYdEYaiRHctV-XouwM1QY4MBAuJlpJMs=.p7b", "signAlg": "SHA256withECDSA", "storeFile": "C:\\Users\\<USER>\\.ohos\\config\\default_ohos_efqy5cCLQ5GOYdEYaiRHctV-XouwM1QY4MBAuJlpJMs=.p12", "storePassword": "0000001A47E367699944D3EDA8B86507D59B39B92DBFA925579CDB3701FA5D3D9364A9014EE4DE154C87"}}, {"name": "release", "type": "HarmonyOS", "material": {"certpath": "D:/workspace/dev/harmonyOs/yyb_flutter/ohos_cert/eyyb.cer", "storePassword": "00000018175DF4AF554B30DF8159AD878563C1CCD413C446B80C5776464D81934C95DC9A7DF93805", "keyAlias": "key", "keyPassword": "00000018A4839ADCDF8B9E142BA80A4886911B152C32EFB6280E2F1591C8A806ABE5F1004ADA9BD2", "profile": "D:/workspace/dev/harmonyOs/yyb_flutter/ohos_cert/eyybRelease.p7b", "signAlg": "SHA256withECDSA", "storeFile": "D:/workspace/dev/harmonyOs/yyb_flutter/ohos_cert/.p12"}}, {"name": "debug", "type": "HarmonyOS", "material": {"storeFile": "D:/workspace/dev/harmonyOs/yyb_flutter/ohos_cert/debug/.p12", "storePassword": "000000180AC1BF370F503990122B15CD363F35611FFB2DB3A862D8FB218CCF3E11EFB8D2968F0E64", "keyAlias": "key", "keyPassword": "00000018F31D03609CD98AF711E4527715812D5BC631A12B28EEB78F3C721B12D8F57ECF74E9D861", "signAlg": "SHA256withECDSA", "profile": "D:/workspace/dev/harmonyOs/yyb_flutter/ohos_cert/debug/eyyb_debug.p7b", "certpath": "D:/workspace/dev/harmonyOs/yyb_flutter/ohos_cert/debug/eyyb_debug.cer"}}], "products": [{"name": "default", "signingConfig": "debug", "compatibleSdkVersion": "5.0.0(12)", "runtimeOS": "HarmonyOS", "buildOption": {"strictMode": {"useNormalizedOHMUrl": true}}}]}, "modules": [{"name": "entry", "srcPath": "./entry", "targets": [{"name": "default", "applyToProducts": ["default"]}]}]}, "CONFIG_PROPERTIES": {"enableSignTask": true, "skipNativeIncremental": false, "hvigor.keepDependency": true}, "OVERALL_PROJECT_PATHS": ["D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos"], "BUILD_CACHE_DIR": ""}, "version": 1}