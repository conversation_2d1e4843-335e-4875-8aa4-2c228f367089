import AAID from "@hms:core.AAID";
import hilog from "@ohos:hilog";
export function isEmpty(j10) {
    if (j10 === undefined || j10 === null || j10 === '') {
        return true;
    }
    return false;
}
export async function getAAID() {
    try {
        const i10 = await AAID.getAAID();
        return i10;
    }
    catch (g10) {
        let h10 = g10;
        hilog.error(0x0000, 'testTag', 'Get AAID catch error: %{public}d %{public}s', h10.code, h10.message);
    }
    return '';
}
