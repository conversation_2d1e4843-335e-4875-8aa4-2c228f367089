part of 'page_content_resolver.dart';

//去练习
void goToExercise(InAppWebViewController webViewController,
    JsBridgeDataBean data, String content, WidgetRef ref) {
//考前突破-去练习
  ref.read(exerciseReportPageControllerProvider.notifier).goToExercise(
      moduleId: data.moduleConfigId?.toString(),
      unitId: data.unitId?.toString(),
      unitName: data.unitName?.toString(),
      selectLever: data.selectLever?.toString());
}

//待订正错题做题-英语
void goWrongExercise(InAppWebViewController webViewController,
    JsBridgeDataBean data, String content, WidgetRef ref) {
  Logger.info("============ wrong exercise:$content");
  String from = data.from?.toString() ?? "";
  var userInfo = ref.read(userInfoNotifierProvider);
  String userId = userInfo.userId;
  switch (from) {
    case "1":
      BaseApiRepository.querytodayerrorqn(userId: userId, subject: "english")
          .then((value) {
        if (value.isSuccess && value.isDataNotNull) {
          toPage(RouteName.wrongExercisePage,
                  extra: WrongExercisePageParam(
                      questions: value.dataNotNull,
                      type: "1",
                      resourceName: data.resourceName))
              .then((value) {
            sendToJs(webViewController, methodName: "onResume");
          });
        } else {
          showToast("未获取到数据");
        }
      });
      break;
    // case "2":
    //   break;
    case "3":
      String resourceId = data.resourceId?.toString() ?? "";
      String bookId = "";
      String unitId = "";
      if (resourceId.isNotEmpty) {
        var rarr = resourceId.split("#@#");
        if (rarr.isNotEmpty) {
          bookId = rarr[0];
          if (rarr.length > 1) {
            unitId = rarr[1];
          }
        }
      }
      BaseApiRepository.querynorevisionerrorqn(
              subject: "english",
              type: data.type?.toString() ?? "0",
              qyType: data.qyType ?? "0",
              labelId: data.labelId?.toString(),
              userId: userId,
              bookId: bookId,
              unitId: unitId)
          .then((value) {
        if (value.isSuccess && value.isDataNotNull) {
          toPage(RouteName.wrongExercisePage,
                  extra: WrongExercisePageParam(
                      questions: value.dataNotNull,
                      type: "3",
                      resourceName: data.resourceName))
              .then((value) {
            sendToJs(webViewController, methodName: "onResume");
          });
        } else {
          showToast("未获取到数据");
        }
      });
      break;
    // case "4":
    //   break;
    // case "5":
    //   break;
    default:
      showToast("敬请期待");
  }
}

void specialReport(InAppWebViewController webViewController,
    JsBridgeDataBean data, String content) {
  ExamInfo examInfo = ExamInfo.fromJson(data.toJson());
  examInfo.moduleId = data.moduleConfigId;
  BaseApiRepository.queryprogressdata(id: data.id, type: data.category)
      .then((value) {
    if (value.isSuccess && value.isDataNotNull) {
      QueryProgressDataModel progressModel = value.dataNotNull;
      List<String> userAnswer =
          progressModel.progress?.studentResult?.split("#@") ?? [];
      List<String> answer =
          progressModel.progress?.sysResult?.split("#@") ?? [];
      if (progressModel.progress?.examPaperId?.isNotEmpty ?? false) {
        BaseApiRepository.queryappexampaper(
                id: progressModel.progress?.examPaperId ?? "")
            .then((examResponse) {
          if (examResponse.isSuccess && examResponse.isDataNotNull) {
            AppExamPaperModel model = examResponse.dataNotNull;
            var dataList = model.data ?? [];

            Map<String, AnswerItem> userAnswerMap = {};
            List<SectionQuestionItem> result = [];
            if (model.volumeStructure == 0) {
              var qlist = model.questionList ?? [];
              for (int i = 0; i < qlist.length; i++) {
                QuestionList a = qlist[i];
                String id = a.id ?? "";
                if (userAnswer.length > i) {
                  Logger.info(
                      "=========== userAnswer: ${userAnswer.length}, i:$i");
                  userAnswerMap[id] = AnswerItem.fromRecord(
                      userAnswer: userAnswer[i].split(";"),
                      questionList: a,
                      answer: answer[i].split(";"));
                }
                result.add(SectionQuestionItem.question(
                  questionList: a, questionIndex: i + 1,
                  // subSectionName: null, childName: null, childScoreVal: null
                ));
              }
              toPage(RouteName.unitEvaluationReport,
                      extra: UnitEvaluationReportPageParam(
                          costSeconds:
                              progressModel.progress?.costSecond?.toInt() ?? 0,
                          examItem: ExamItem(
                            examName: data.examName,
                            examDuration: data.examDuration,
                            totalScore: data.totalScore,
                            accuracy: data.accuracy?.toString(),
                          ),
                          userAnswerMap: userAnswerMap,
                          isFromExam: false,
                          userScore:
                              progressModel.progress?.score?.toDouble() ?? 0,
                          questionItems: result,
                          notVolumeStructurePageParam:
                              NotVolumeStructurePageParam(
                                  examInfo: examInfo, paperModel: model)))
                  .then((value) {
                sendToJs(webViewController, methodName: "onResume");
              });
            } else {
              int index = 0;
              for (int i = 0; i < dataList.length; i++) {
                Data d = dataList[i];
                List<ChildList> children = d.childList ?? [];
                for (int j = 0; j < children.length; j++) {
                  ChildList c = children[j];
                  if (j == 0) {
                    //添加章节开始页数据
                    result.add(SectionQuestionItem.subSectionStart(
                        subSectionName: d.subsectionName,
                        subsectionScoreVal: d.subsectionScoreVal,
                        childName: c.childName,
                        childScoreVal: c.childScoreVal));
                  } else {
                    result.add(SectionQuestionItem.childStart(
                      childName: c.childName,
                      subSectionName: d.subsectionName,
                      childScoreVal: c.childScoreVal,
                    ));
                  }
                  //添加题目
                  for (QuestionList a in c.questionList ?? []) {
                    String id = a.id ?? "";
                    if (userAnswer.length > index) {
                      userAnswerMap[id] = AnswerItem.fromRecord(
                          userAnswer: userAnswer[index].split(";"),
                          questionList: a,
                          answer: answer[index].split(";"));
                    }
                    index++;
                    var question = SectionQuestionItem.question(
                      questionList: a,
                      questionIndex: index,
                    );
                    result.add(question);
                  }
                }
              }
              toPage(RouteName.unitEvaluationReport,
                  extra: UnitEvaluationReportPageParam(
                    costSeconds:
                        progressModel.progress?.costSecond?.toInt() ?? 0,
                    examItem: ExamItem(
                      examName: data.examName,
                      examDuration: data.examDuration,
                      totalScore: data.totalScore,
                      accuracy: data.accuracy?.toString(),
                    ),
                    userAnswerMap: userAnswerMap,
                    isFromExam: false,
                    userScore: progressModel.progress?.score?.toDouble() ?? 0,
                    questionItems: result,
                    volumeStructurePageParam: VolumeStructurePageParam(
                      examInfo: examInfo,
                      paperModel: model,
                    ),
                  )).then((value) {
                sendToJs(webViewController, methodName: "onResume");
              });
            }
          } else {
            showToast("数据不存在");
          }
        });
      } else {
        Map<String, AnswerItem> userAnswerMap = {};
        final List<QuestionList> question = progressModel.question ?? [];
        for (int i = 0; i < question.length; i++) {
          QuestionList q = question[i];
          if (userAnswer.length > i) {
            userAnswerMap[q.id ?? ""] = AnswerItem.fromRecord(
                userAnswer: userAnswer[i].split(";"),
                questionList: q,
                answer: answer[i].split(";"));
          }
        }
        ExamInfo examInfo = ExamInfo.fromJson(data.toJson());
        examInfo.moduleId = data.moduleConfigId;

        toPage(RouteName.exerciseReport,
                extra: ExerciseReportPageParam(
                    costSeconds:
                        progressModel.progress?.costSecond?.toInt() ?? 0,
                    questions: question,
                    userAnswerMap: userAnswerMap,
                    exerciseExamPaperModel: ExerciseExamPaperModel(
                        moduleId: data.moduleConfigId,
                        unitId: progressModel.progress?.breakthroughUnitId,
                        unitName: data.examName?.toString(),
                        name: data.gearName?.toString(),
                        levelName: data.levelName?.toString()),
                    isFromExam: false))
            .then((value) {
          sendToJs(webViewController, methodName: "onResume");
        });
      }
    } else {
      showToast("未获取到数据");
    }
  });
}

void goToAnswer(InAppWebViewController webViewController, JsBridgeDataBean data,
    String content) {
  ExamInfo examInfo = ExamInfo.fromJson(data.toJson());
  examInfo.moduleId = data.moduleConfigId;
  if ("record" == examInfo.source) {
    //部分完成，继续答题
    BaseApiRepository.querypartexamprogress(id: examInfo.examPaperId ?? "")
        .then((value) {
      if (value.isSuccess && value.isDataNotNull) {
        PartExamProgressModel partExamProgressModel = value.dataNotNull;
        List<String> userAnswer =
            partExamProgressModel.studentResult?.split("#@") ?? [];
        List<String> answer =
            partExamProgressModel.sysResult?.split("#@") ?? [];
        List<String> tf = partExamProgressModel.tf?.split(",") ?? [];

        BaseApiRepository.queryappexampaper(
                id: partExamProgressModel.examPaperId ?? "")
            .then((examResponse) {
          if (examResponse.isSuccess && examResponse.isDataNotNull) {
            AppExamPaperModel model = examResponse.dataNotNull;

            Map<String, AnswerItem> userAnswerMap = {};
            if (model.volumeStructure == 0) {
              var qlist = model.questionList ?? [];
              for (int i = 0; i < qlist.length; i++) {
                QuestionList a = qlist[i];
                String id = a.id ?? "";
                if (userAnswer.length > i) {
                  Logger.info(
                      "=========== userAnswer: ${userAnswer.length}, i:$i");
                  userAnswerMap[id] = AnswerItem.fromRecord(
                      userAnswer: userAnswer[i].split(";"),
                      questionList: a,
                      answer: answer[i].split(";"));
                }
              }
            } else {
              int index = 0;
              var dataList = model.data ?? [];
              for (int i = 0; i < dataList.length; i++) {
                Data d = dataList[i];
                List<ChildList> children = d.childList ?? [];
                for (int j = 0; j < children.length; j++) {
                  ChildList c = children[j];
                  //添加题目
                  for (QuestionList a in c.questionList ?? []) {
                    String id = a.id ?? "";
                    if (userAnswer.length > index) {
                      userAnswerMap[id] = AnswerItem.fromRecord(
                          userAnswer: userAnswer[index].split(";"),
                          questionList: a,
                          answer: answer[index].split(";"));
                    }
                    index++;
                  }
                }
              }
            }

            if (model.volumeStructure == 0) {
              //纯小题
              toPage(RouteName.notVolumeStructureExam,
                      extra: NotVolumeStructurePageParam(
                          paperModel: model,
                          examInfo: examInfo,
                          costSeconds:
                              partExamProgressModel.costSecond?.toInt() ?? 0,
                          userAnswerMap: userAnswerMap))
                  .then((value) {
                if (value != null && value is bool && value) {
                  sendToJs(webViewController, methodName: "onResume");
                }
              });
            } else {
              //卷结构大题组合
              toPage(RouteName.volumeStructureExam,
                      extra: VolumeStructurePageParam(
                          examInfo: examInfo,
                          paperModel: model,
                          costSeconds:
                              partExamProgressModel.costSecond?.toInt() ?? 0,
                          userAnswerMap: userAnswerMap))
                  .then((value) {
                if (value != null && value is bool && value) {
                  sendToJs(webViewController, methodName: "onResume");
                }
              });
            }
          } else {
            showToast("数据不存在");
          }
        });
      }
    });
  } else {
    BaseApiRepository.queryappexampaper(id: examInfo.examPaperId ?? "")
        .then((value) {
      if (value.isSuccess && value.isDataNotNull) {
        AppExamPaperModel model = value.dataNotNull;
        if (model.volumeStructure == 0) {
          //纯小题
          List<String> images = [];
          for (int i = 1; i <= 68; i++) {
            String imageName = "assets/images/easyword_anim_ready_$i.png";
            images.add(AssetsUtils.wrapAsset(imageName));
          }
          showSmartDialog(
              ImageSwitchAnimation(
                images: images,
                width: 250.r,
                height: 250.r,
                loopCount: 1,
                autoPlay: true,
                duration: Duration(milliseconds: 50),
                onFinished: () {
                  dismissDialog();
                  toPage(RouteName.notVolumeStructureExam,
                      extra: NotVolumeStructurePageParam(
                        paperModel: model,
                        examInfo: examInfo,
                      ));
                },
              ),
              clickMaskDismiss: false,
              backDismiss: false);
        } else {
          //卷结构大题组合
          toPage(RouteName.volumeStructureExam,
              extra: VolumeStructurePageParam(
                examInfo: examInfo,
                paperModel: model,
              ));
        }
      } else {
        showToast("数据不存在");
      }
    });
  }
}

void toStuToAnswer(InAppWebViewController webViewController,
    JsBridgeDataBean data, String content, WidgetRef ref) {
  //      ExamInfo examInfo = ExamInfo.fromJson(data.toJson());
  // examInfo.moduleId = data.moduleConfigId;
  BaseApiRepository.queryappexampaper(id: data.examPaperId ?? "").then((value) {
    if (value.isSuccess) {
      if (value.isDataNotNull) {
        AppExamPaperModel item = value.dataNotNull;
        if (data.examName?.isNotEmpty ?? false) {
          //使用H5给的测试卷名称
          item.examName = data.examName;
        }
        String id = data.id?.toString() ?? "";
        StatisticsInfoBean statisticsInfoBean = StatisticsInfoBean(
          id: data.id,
          seriesId: data.seriesId?.toString(),
          seriesName: data.seriesName?.toString(),
          schoolId: data.schoolId?.toString(),
          clazzId: data.clazzId?.toString(),
          examDuration: data.examDuration?.toString(),
          examPaperId: data.examPaperId?.toString(),
          examName: data.examName?.toString(),
          areaCode: data.areaCode?.toString(),
          resourcesId: data.resourceId?.toString(),
          type: data.type?.toString(),
          endTime: data.endTime?.toString(),
        );
        if (id.isEmpty) {
          if (item.volumeStructure?.toString() == '0') {
            showToast("试卷结构错误！");
          } else {
            //StatisticsTestActivity
            // startActivity(StatisticsTestActivity.newIntent(WebActivity.this, item, statisticsInfoBean));

            toPage(RouteName.statisticsTestPage,
                    extra: StatisticsTestPageParam(
                        appExamPaperModel: item,
                        statisticsInfoBean: statisticsInfoBean))
                .then((value) {
              if (value != null && value is bool && value) {
                sendToJs(webViewController, methodName: "onResume");
              }
            });
          }
        } else {
          BaseApiRepository.querypartunifiedtestprogress(id: id).then((value) {
            if (value.isSuccess) {
              if (value.isDataNotNull) {
                StatisticsTestSubmintItem submitItem = value.dataNotNull;
                statisticsInfoBean.remainingTime = submitItem.remainingTime;
                List<String>? studentResults =
                    submitItem.studentResult?.split("#@");
                List<String>? sysResults = submitItem.sysResult?.split("#@");
                List<String>? tfs = submitItem.tf?.split(",");
                List<String>? answerScores = submitItem.answerScore?.split(",");
                List<String> ur = [];
                if (studentResults?.isNotEmpty ?? false) {
                  for (int i = 0; i < studentResults!.length; i++) {
                    String result = studentResults[i];
                    if ((sysResults?.length ?? 0) > i + 1) {
                      String sresult = sysResults![i];
                      if (sresult.isNotEmpty) {
                        ur.add(result);
                      }
                    } else {
                      if (result.isNotEmpty) {
                        ur.add(result);
                      }
                    }
                  }
                }

                //StatisticsTestActivity
                // startActivity(StatisticsTestActivity.newIntent(WebActivity.this, item, statisticsInfoBean));

                toPage(RouteName.statisticsTestPage,
                    extra: StatisticsTestPageParam(
                      appExamPaperModel: item,
                      statisticsInfoBean: StatisticsInfoBean(
                        id: data.id,
                        seriesId: data.seriesId?.toString(),
                        seriesName: data.seriesName?.toString(),
                        schoolId: data.schoolId?.toString(),
                        clazzId: data.clazzId?.toString(),
                        examDuration: data.examDuration?.toString(),
                        examPaperId: data.examPaperId?.toString(),
                        examName: data.examName?.toString(),
                        areaCode: data.areaCode?.toString(),
                        resourcesId: data.resourceId?.toString(),
                        type: data.type?.toString(),
                        endTime: data.endTime?.toString(),
                      ),
                      userAnswer: ur,
                      answer: sysResults,
                    )).then((value) {
                  if (value != null && value is bool && value) {
                    sendToJs(webViewController, methodName: "onResume");
                  }
                });
              } else {
                showToast("题目数据异常！");
              }
            }
          });
        }
      } else {
        showToast("题目数据异常！");
      }
    }
  });
}

void toWordMatch(InAppWebViewController webViewController,
    JsBridgeDataBean data, String content, WidgetRef ref) {
  _loadWordMatchData(
      userInfo: ref.read(userInfoNotifierProvider),
      type: data.resourceId,
      activityId: data.id,
      isFirst: true,
      count: 3);
}

Future _loadWordMatchData({
  required String? activityId,
  required String? type,
  required UserInfoModel userInfo,
  bool isFirst = false,
  required int count,
}) {
  showLoading();
  return BaseApiRepository.queryQuestions(
          activityId: activityId,
          ids: "",
          wordIds: "",
          versionId: type,
          userId: userInfo.userId)
      .then((value) {
    if (value.isSuccess) {
      if (value.isDataNotNull) {
        ActivityQuestionResponseModel data = value.dataNotNull;
        int? times = data.times;
        List<QuestionList> questionList = data.questions ?? [];
        if (isFirst && (times ?? 0) > 200) {
          isFirst = false;
          // 巧记单词活动只加载4次, 为什么限制次数. --- V7.9.0 20241104调整为6词
          count = 6;
        }
        if (questionList.isNotEmpty && count > 1) {
          count--;
          _loadWordMatchData(
              activityId: activityId,
              type: type,
              userInfo: userInfo,
              isFirst: false,
              count: count);
        } else {
          dismissLoading();
          //显示倒计时
          List<String> images = [];
          for (int i = 1; i <= 68; i++) {
            String imageName = "assets/images/easyword_anim_ready_$i.png";
            images.add(AssetsUtils.wrapAsset(imageName));
          }
          showSmartDialog(
              ImageSwitchAnimation(
                images: images,
                width: 250.r,
                height: 250.r,
                loopCount: 1,
                autoPlay: true,
                duration: Duration(milliseconds: 50),
                onFinished: () {
                  dismissDialog();
                  toPage(RouteName.wordMatch,
                      extra: WordMatchPageParam(
                          activityId: activityId,
                          type: type,
                          times: times,
                          questionList: questionList));

                  //for test
                  // MatchResultPageParam matchResultPageParam =
                  //     MatchResultPageParam(
                  //         activityId: activityId ?? "",
                  //         submitAnswerInfoModel: SubmitAnswerInfoModel(number: 3,totalNum: 8),
                  //         times:  times ?? 0,
                  //         successQuestionCount: 2,
                  //         failedQuestionCount: 3,
                  //         rightRate:
                  //            ( (2 / (5))
                  //                      *
                  //                 100).floor().toDouble(),
                  //         failCount: ['a','w','match',"sdfsdfsdfsdfsdfsdkfjsdlfjsldfjasl;dkfj;sdlkfj",'122','23','23',
                  //         'hello','test']);
                  // pushReplacePage(RouteName.matchResult,
                  //     extra: matchResultPageParam);
                },
              ),
              clickMaskDismiss: false,
              backDismiss: false);
        }
      } else {
        dismissLoading();
        showSmartDialog(SimpleAlertDialog(
          title: "",
          desc: "数据加载失败",
          cancelText: "退出",
          confirmText: "重试",
          cancel: () {
            dismissDialog();
            back();
          },
          confirm: () => _loadWordMatchData(
              activityId: activityId,
              type: type,
              userInfo: userInfo,
              count: count),
        ));
      }
    }
  }).onError((error, stackTrace) {
    dismissLoading();
    Logger.error("====== 请求失败，error:${error.toString()}", stackTrace);
  });
}

//口语训练
void mcStuToAnswer(InAppWebViewController webViewController,
    JsBridgeDataBean data, String content, WidgetRef ref) {
  String? id = data.id;
  String? examPaperId = data.examPaperId;
  String? accuracy = data.accuracy?.toString();
  String? examTypeName = data.examTypeName;
  String? jointExamineName = data.jointExamineName;
  String? dedicatedRegion = data.dedicatedRegion;
  String? moduleConfigId = data.moduleConfigId;
  String? subId = data.subId;
  String? breakthroughId = data.breakthroughId;
  String? unitId = data.unitId;
  //单元id + subId + breakthroughId + unitId
  StatisticsInfoBean statisticsInfoBean = new StatisticsInfoBean(
    id: id,
    moduleId: moduleConfigId,
    examTypeName: examTypeName,
    jointExamineName: jointExamineName,
    dedicatedRegion: dedicatedRegion,
  );

  BaseApiRepository.queryappexampaper(id: examPaperId ?? "").then((value) {
    if (value.isSuccess) {
      if (value.isDataNotNull) {
        AppExamPaperModel item = value.dataNotNull;
        if (data.examName?.isNotEmpty ?? false) {
          //使用H5给的测试卷名称
          item.examName = data.examName;
        }
        statisticsInfoBean.dedicatedRegion = item.dedicatedRegion;
        item.accuracy = accuracy;
        if (id?.isEmpty ?? true) {
          if (item.volumeStructure?.toString() == '0') {
            showToast("试卷结构错误！");
          } else {
            //StatisticsTestActivity
            // startActivity(StatisticsTestActivity.newIntent(WebActivity.this, item, statisticsInfoBean));

            toPage(RouteName.statisticsTestPage,
                extra: StatisticsTestPageParam(
                  appExamPaperModel: item,
                  statisticsInfoBean: statisticsInfoBean,
                  testType: StatisticsTestPageType.spokeTest,
                )).then((value) {
              if (value != null && value is bool && value) {
                sendToJs(webViewController, methodName: "onResume");
              }
            });
          }
        } else {
          BaseApiRepository.querypartoralmtestpro(id: id).then((value) {
            if (value.isSuccess) {
              if (value.isDataNotNull) {
                statisticsInfoBean.dedicatedRegion = item.dedicatedRegion;
                StatisticsTestSubmintItem submitItem = value.dataNotNull;
                statisticsInfoBean.remainingTime = submitItem.remainingTime;
                List<String>? studentResults =
                    submitItem.studentResult?.split("#@");
                List<String>? sysResults = submitItem.sysResult?.split("#@");
                List<String>? tfs = submitItem.tf?.split(",");
                List<String>? answerScores = submitItem.answerScore?.split(",");
                List<String> ur = [];
                if (studentResults?.isNotEmpty ?? false) {
                  for (int i = 0; i < studentResults!.length; i++) {
                    String result = studentResults[i];
                    Logger.info("============ result:$result, i:$i");
                    if ((sysResults?.length ?? 0) >= i + 1) {
                      String sresult = sysResults![i];
                      if (sresult.isNotEmpty) {
                        ur.add(result);
                      } else {
                        if (result.isNotEmpty) {
                          ur.add(result);
                        }
                      }
                    } else {
                      if (result.isNotEmpty) {
                        ur.add(result);
                      }
                    }
                  }
                }
                Logger.info(
                    "=================  studentResult:$studentResults, ur:$ur");
                //StatisticsTestActivity
                // startActivity(StatisticsTestActivity.newIntent(WebActivity.this, item, statisticsInfoBean));

                toPage(RouteName.statisticsTestPage,
                    extra: StatisticsTestPageParam(
                      appExamPaperModel: item,
                      statisticsInfoBean: statisticsInfoBean,
                      userAnswer: ur,
                      answer: sysResults,
                      testType: StatisticsTestPageType.spokeTest,
                    )).then((value) {
                  if (value != null && value is bool && value) {
                    sendToJs(webViewController, methodName: "onResume");
                  }
                });
              } else {
                showToast("题目数据异常！");
              }
            }
          });
        }
      } else {
        showToast("题目数据异常！");
      }
    }
  });
}

//人机对话
void openMdExercise(InAppWebViewController webViewController,
    JsBridgeDataBean data, String content, WidgetRef ref) {
  String? practiceId = data.id;
  String? bookId = data.bookId;
  String? unitId = data.unitId;
  String? unitName = data.unitName;
  int? type = data.type;
  //0 开始答题  1 继续答题  2 查看报告
  if (type == 0) {
    BaseApiRepository.selectquestionbypractice(practiceId: practiceId)
        .then((v) {
      if (v.isSuccess && v.isDataNotNull) {
        //toPage
        toPage(RouteName.wrongExercisePage,
            extra: WrongExercisePageParam(
                questions: v.dataNotNull,
                type: "1",
                resourceName: unitName ?? ""  ));
      } else {
        showToast(v.msg ?? "没有题目数据！");
      }
    });
  } else if (type == 1) {
  } else {}
}
