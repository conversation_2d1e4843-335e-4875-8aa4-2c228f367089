import fs from '@ohos:file.fs';
import zlib from '@ohos:zlib';
import JSO<PERSON> from "@ohos:util.json";
export class FileUtils {
    static async copyRawFile2Sandbox(w8, x8, y8) {
        return new Promise((a9, b9) => {
            w8.resourceManager.getRawFd(x8, (d9, e9) => {
                try {
                    console.log("::ssound_log:: copyRawFile2Sandbox -> " + x8, y8);
                    let g9 = fs.openSync(y8, fs.OpenMode.CREATE | fs.OpenMode.READ_WRITE);
                    let h9 = 4096;
                    let i9 = new ArrayBuffer(h9);
                    let j9 = 0, k9 = 0, l9 = 0;
                    while (k9 = fs.readSync(e9.fd, i9, { offset: e9.offset + j9, length: h9 })) {
                        l9 += k9;
                        fs.writeSync(g9.fd, i9, { offset: j9, length: k9 });
                        j9 = j9 + k9;
                        if ((e9.length - l9) < h9) {
                            h9 = e9.length - l9;
                        }
                    }
                    fs.close(g9.fd);
                    a9(y8);
                }
                catch (f9) {
                    b9(f9);
                }
            });
        });
    }
    static getSandboxFileList(r8, s8) {
        console.log('::ssound_log::查看文件列表:' + r8);
        let t8 = {
            recursion: s8 ?? false,
            listNum: 0,
            filter: {
                suffix: [".png", ".docx", ".txt", ".zip", ".wav", ".bin", ".cfg"],
                displayName: ["*"],
                fileSizeOver: 0,
                lastModifiedAfter: new Date(0).getTime()
            }
        };
        let u8 = fs.listFileSync(r8, t8);
        for (let v8 = 0; v8 < u8.length; v8++) {
            console.info(`::ssound_log:: The name of file: ${u8[v8]}`);
        }
        return u8;
    }
    static async getCrashText(m8) {
        let n8 = {
            encoding: 'utf-8'
        };
        if (!fs.accessSync(m8)) {
            console.log("::ssound_log:: No such file or directory ", m8);
            return;
        }
        let o8 = fs.readLinesSync(m8, n8);
        while (true) {
            try {
                let q8 = o8.next();
                if (q8.done) {
                    return;
                }
                console.info("::ssound_log:: native_crash.txt content: " + q8.value);
            }
            catch (p8) {
                console.info("::ssound_log:: native_crash.txt error: " + JSON.stringify(p8));
                if (p8.code === 13900020) {
                    return;
                }
            }
        }
    }
    static getResourcesPath(d8) {
        return new Promise(async (f8, g8) => {
            const h8 = await FileUtils.unzipRawFileHandler(d8, 'resources.zip');
            console.log('::ssound_log:: 查看文件列表:' + h8);
            const i8 = `${h8}/eval/bin`;
            let j8 = FileUtils.getSandboxFileList(i8);
            ;
            if (j8 === undefined) {
                g8('文件读取错误');
            }
            const k8 = [];
            for (let l8 = 0; l8 < j8.length; l8++) {
                console.info(`::ssound_log:: The name of file: ${j8[l8]}`);
                k8.push(`${i8}/${j8[l8]}`);
            }
            f8(k8);
        });
    }
    static async unzipRawFileHandler(o7, p7) {
        return new Promise(async (r7, s7) => {
            const t7 = o7.filesDir + "/" + p7;
            console.log("::ssound_log:: 沙箱路径：" + t7);
            let u7 = o7.tempDir;
            const v7 = u7 + '/eval';
            const w7 = fs.accessSync(v7);
            if (w7) {
                fs.rmdirSync(v7);
            }
            console.log("::ssound_log:: 压缩文件路径：" + u7);
            await FileUtils.copyRawFile2Sandbox(o7, p7, t7);
            try {
                zlib.decompressFile(t7, u7, (b8) => {
                    if (b8 !== null) {
                        const c8 = `::ssound_log:: decompressFile failed. code is ${b8.code}, message is ${b8.message}`;
                        console.error(c8);
                        s7(c8);
                    }
                    else {
                        r7(u7);
                    }
                });
            }
            catch (x7) {
                let y7 = x7.code;
                let z7 = x7.message;
                console.error(`::ssound_log:: decompressFile failed. code is ${y7}, message is ${z7}`);
            }
            o7.resourceManager.closeRawFd(p7);
        });
    }
}
