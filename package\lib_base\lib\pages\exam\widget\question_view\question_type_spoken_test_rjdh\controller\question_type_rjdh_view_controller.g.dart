// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'question_type_rjdh_view_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$questionTypeRjdhViewControllerHash() =>
    r'cc94ac96f9c89ca87ee5356e8081bdd59d02c134';

/// See also [QuestionTypeRjdhViewController].
@ProviderFor(QuestionTypeRjdhViewController)
final questionTypeRjdhViewControllerProvider =
    AutoDisposeNotifierProvider<QuestionTypeRjdhViewController, int>.internal(
  QuestionTypeRjdhViewController.new,
  name: r'questionTypeRjdhViewControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$questionTypeRjdhViewControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$QuestionTypeRjdhViewController = AutoDisposeNotifier<int>;
// ignore_for_file: unnecessary_raw_strings, subtype_of_sealed_class, invalid_use_of_internal_member, do_not_use_environment, prefer_const_constructors, public_member_api_docs, avoid_private_typedef_functions
