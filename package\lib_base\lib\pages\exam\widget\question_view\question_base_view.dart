import 'package:flutter/cupertino.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/model/question_list.dart';
import 'package:lib_base/pages/exam/widget/question_view/question_type_chiose/question_type_chiose_view.dart';
import 'package:lib_base/pages/exam/widget/question_view/question_type_con/question_type_con_view.dart';
import 'package:lib_base/pages/exam/widget/question_view/question_type_enum.dart';
import 'package:lib_base/pages/exam/widget/question_view/question_type_express/question_type_express_view.dart';
import 'package:lib_base/pages/exam/widget/question_view/question_type_fill/question_type_fill_view.dart';
import 'package:lib_base/pages/exam/widget/question_view/question_type_match/question_type_match_view.dart';
import 'package:lib_base/pages/exam/widget/question_view/question_type_spoken_test/question_type_spoken_test_view.dart';
import 'package:lib_base/pages/exam/widget/question_view/question_type_spoken_test_rjdh/question_type_rjdh_view.dart';
import 'package:lib_base/pages/exam/widget/question_view/word_listen_write/word_listen_write_view.dart';

//NewBaseQuestionFragment
class QuestionBaseView extends StatelessWidget {
  final QuestionList question;
  const QuestionBaseView({
    super.key,
    required this.question,
  });

  @override
  Widget build(BuildContext context) {
    var questionType = question.questionType;
    Logger.info("===questionType:$questionType, question:${question.toJson()}");
    Widget? child;
    if (questionType == QuestionType.question_type_fill) {
      child = QuestionTypeFillView(question: question);
    } else if (questionType == QuestionType.question_type_chiose) {
      child = QuestionTypeChoiseView(question: question);
    } else if (questionType == QuestionType.question_type_match) {
      //连线题
      child = QuestionTypeMatchView(
        question: question,
      );
    } else if (questionType == QuestionType.question_type_con) {
      //根据图片选择答案
      child = QuestionTypeConView(question: question);
    } else if (questionType == QuestionType.question_type_spoken_test) {
      String twouserQuestionType = question.twouserQuestionType ?? "";
      if (twouserQuestionType == '') {
        child = QuestionTypeRjdhView(question: question);
      } else {
        child = QuestionTypeSpokenTestView(question: question);
      }
    } else if (questionType == QuestionType.question_type_express) {
      child = QuestionTypeExpressView(question: question);
    } else {
      //没有questionType， 单词听写
      child = WordListenWriteView(question: question);
    }
    return Padding(
      padding: EdgeInsets.only(
          top: 18, bottom: MediaQuery.of(context).padding.bottom),
      child: child,
    );
  }
}
