import { FileUtils } from "@normalized:N&&&singsound/src/main/ets/a/p/s&1.0.0";
export function parseCNGResourcePath(a10) {
    for (let b10 = 0; b10 < a10.length; b10++) {
        const c10 = a10[b10];
        if (c10.fileName.startsWith("chn")) {
            const d10 = c10.fileName.split(".");
            let e10 = "";
            if (d10.length >= 2) {
                const f10 = d10[1];
                if (f10 === "wrd") {
                    e10 = "cn.word.score";
                }
                else if (f10 === "snt") {
                    e10 = "cn.sent.score";
                }
                else {
                    e10 = `cn.${f10}.score`;
                }
                c10.key = e10;
            }
            else {
            }
        }
    }
    return a10;
}
export function parseENGResourcePath(u9) {
    for (let v9 = 0; v9 < u9.length; v9++) {
        const w9 = u9[v9];
        if (w9.fileName.startsWith("eng")) {
            const x9 = w9.fileName.split(".");
            let y9 = "";
            if (x9.length >= 2) {
                const z9 = x9[1];
                if (z9 === "wrd") {
                    y9 = "en.word.score";
                }
                else if (z9 === "snt") {
                    y9 = "en.sent.score";
                }
                else {
                    y9 = `en.${z9}.score`;
                }
                w9.key = y9;
            }
            else {
            }
        }
    }
    return u9;
}
export async function listRawFiles(m9) {
    try {
        const o9 = m9.resourceManager;
        if (!o9) {
            console.error('::ssound_log:: Failed to get ResourceManager');
            return [];
        }
        const p9 = await FileUtils.getResourcesPath(m9);
        const q9 = [];
        for (const r9 of p9) {
            const s9 = r9.split('/');
            const t9 = s9.length - 1;
            q9.push({
                fileName: s9[t9],
                filePath: r9,
            });
        }
        return q9;
    }
    catch (n9) {
        console.error('::ssound_log:: Error while listing raw files:', n9);
    }
    return [];
}
