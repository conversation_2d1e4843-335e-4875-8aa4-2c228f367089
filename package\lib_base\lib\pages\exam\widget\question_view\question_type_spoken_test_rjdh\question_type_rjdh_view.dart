

import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/src/consumer.dart';
import 'package:lib_base/pages/exam/widget/question_view/base_view/base_question_view_state.dart';

class QuestionTypeRjdhView extends BaseQuestionView {

  QuestionTypeRjdhView({required super.question});


  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
     return _QuestionTypeRjdhViewState();
  }

}

class _QuestionTypeRjdhViewState extends BaseQuestionViewState<QuestionTypeRjdhView> {
  @override
  initAnswers() {
    // TODO: implement initAnswers
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: ValueListenableBuilder(
          valueListenable: controller.showCheckNotifier,
          builder: (_, state, child) {
            List<Widget> children = [];
            
            
            return Column(
              children: [
                Text("人机对话"),
              ],
            );
          }),
    );
  }
}


